/// <reference types="vite/client" />

interface DatabaseResult {
  success: boolean
  message?: string
  data?: any
  error?: string
  rowCount?: number
}

interface User {
  id: number
  username: string
  created_at: string
}

interface LoginResult {
  success: boolean
  message?: string
  user?: User
  error?: string
  isLocked?: boolean
  lockoutEndTime?: string
  failedAttempts?: number
  remainingAttempts?: number
}

interface AccountStatus {
  username: string
  failedAttempts: number
  isLocked: boolean
  lockedUntil: string | null
  lastFailedLogin: string | null
  remainingLockoutTime: number
}

interface AccountStatusResult {
  success: boolean
  message?: string
  accountStatus?: AccountStatus
  error?: string
}

interface ElectronAPI {
  testDbConnection(): Promise<DatabaseResult>
  getTables(): Promise<DatabaseResult>
  executeQuery(query: string, params?: any[]): Promise<DatabaseResult>
  fetchData(): Promise<any>
  login(username: string, password: string): Promise<LoginResult>
  getUserProfile(userId: number): Promise<LoginResult>
  checkAccountStatus(username: string): Promise<AccountStatusResult>
  unlockAccount(username: string): Promise<DatabaseResult>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
