appId: com.daltonmenezes.electronrouterdom
productName: Eposservice
electronVersion: 21.2.3
directories:
  buildResources: build
icon: src/build/favicon
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - '**/*.{node,dll}'
win:
  executableName: electron-router-dom-basic-example
  icon: src/build/favicon.ico
mac:
  category: public.app-category.developer-tools
  icon: src/build/favicon.icns
  target:
    - dir
linux:
  target:
    - deb
  maintainer: daltonmenezes.com
  synopsis: ${description}
  category: Utility
npmRebuild: false
