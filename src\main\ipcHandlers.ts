import axios from 'axios';
import { ipcMain } from 'electron';
import { Pool } from 'pg';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

// Database configuration
const dbConfig = {
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require',
  ssl: {
    rejectUnauthorized: false
  }
};

// Create a connection pool
const pool = new Pool(dbConfig);

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  // Test database connection
  ipcMain.handle('test-db-connection', async () => {
    try {
      console.log('Testing database connection...');
      const client = await pool.connect();

      // Test with a simple query
      const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
      client.release();

      console.log('Database connection successful!');
      return {
        success: true,
        message: 'Database connection successful',
        data: result.rows[0]
      };
    } catch (error) {
      console.error('Database connection failed:', error);
      return {
        success: false,
        message: 'Database connection failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Execute custom SQL queries
  ipcMain.handle('execute-query', async (event, query: string, params: any[] = []) => {
    try {
      console.log('Executing query:', query);
      const client = await pool.connect();
      const result = await client.query(query, params);
      client.release();

      return {
        success: true,
        data: result.rows,
        rowCount: result.rowCount
      };
    } catch (error) {
      console.error('Query execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get database tables
  ipcMain.handle('get-tables', async () => {
    try {
      console.log('Fetching database tables...');
      const client = await pool.connect();
      const result = await client.query(`
        SELECT table_name, table_schema
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);
      client.release();

      return {
        success: true,
        data: result.rows
      };
    } catch (error) {
      console.error('Failed to fetch tables:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // User authentication with lockout protection
  ipcMain.handle('login', async (event, username: string, password: string) => {
    try {
      console.log('Login attempt for username:', username);
      const client = await pool.connect();

      // First, check if user exists and get lockout status
      const userCheck = await client.query(
        'SELECT id, username, password, created_at, failed_login_attempts, locked_until, last_failed_login FROM m_user WHERE username = $1',
        [username]
      );

      if (userCheck.rows.length === 0) {
        client.release();
        console.log('Login failed: User not found');
        return {
          success: false,
          message: 'Invalid username or password'
        };
      }

      const user = userCheck.rows[0];
      const now = new Date();

      // Check if account is currently locked
      if (user.locked_until && new Date(user.locked_until) > now) {
        const lockoutEndTime = new Date(user.locked_until);
        const remainingMinutes = Math.ceil((lockoutEndTime.getTime() - now.getTime()) / (60 * 1000));

        client.release();
        console.log(`Login failed: Account locked for user ${username}. Unlocks in ${remainingMinutes} minutes`);
        return {
          success: false,
          message: `Account is locked due to multiple failed login attempts. Please try again in ${remainingMinutes} minute(s).`,
          isLocked: true,
          lockoutEndTime: lockoutEndTime.toISOString()
        };
      }

      // Check password
      if (user.password === password) {
        // Successful login - reset failed attempts
        await client.query(
          'UPDATE m_user SET failed_login_attempts = 0, locked_until = NULL, last_failed_login = NULL WHERE id = $1',
          [user.id]
        );

        client.release();
        console.log('Login successful for user:', user.username);
        return {
          success: true,
          message: 'Login successful',
          user: {
            id: user.id,
            username: user.username,
            created_at: user.created_at
          }
        };
      } else {
        // Failed login - increment failed attempts
        const newFailedAttempts = user.failed_login_attempts + 1;
        const maxAttempts = 3;
        const lockoutDurationMinutes = 15; // 15 minutes lockout

        if (newFailedAttempts >= maxAttempts) {
          // Lock the account
          const lockoutEndTime = new Date(now.getTime() + (lockoutDurationMinutes * 60 * 1000));

          await client.query(
            'UPDATE m_user SET failed_login_attempts = $1, locked_until = $2, last_failed_login = $3 WHERE id = $4',
            [newFailedAttempts, lockoutEndTime, now, user.id]
          );

          client.release();
          console.log(`Account locked for user ${username} after ${maxAttempts} failed attempts`);
          return {
            success: false,
            message: `Account has been locked due to ${maxAttempts} failed login attempts. Please try again in ${lockoutDurationMinutes} minutes.`,
            isLocked: true,
            lockoutEndTime: lockoutEndTime.toISOString()
          };
        } else {
          // Update failed attempts count
          await client.query(
            'UPDATE m_user SET failed_login_attempts = $1, last_failed_login = $2 WHERE id = $3',
            [newFailedAttempts, now, user.id]
          );

          client.release();
          const remainingAttempts = maxAttempts - newFailedAttempts;
          console.log(`Login failed for user ${username}. ${newFailedAttempts}/${maxAttempts} failed attempts. ${remainingAttempts} attempts remaining.`);
          return {
            success: false,
            message: `Invalid username or password. ${remainingAttempts} attempt(s) remaining before account lockout.`,
            failedAttempts: newFailedAttempts,
            remainingAttempts: remainingAttempts
          };
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get user profile
  ipcMain.handle('get-user-profile', async (event, userId: number) => {
    try {
      console.log('Fetching user profile for ID:', userId);
      const client = await pool.connect();

      const result = await client.query(
        'SELECT id, username, created_at FROM m_user WHERE id = $1',
        [userId]
      );

      client.release();

      if (result.rows.length > 0) {
        return {
          success: true,
          user: result.rows[0]
        };
      } else {
        return {
          success: false,
          message: 'User not found'
        };
      }
    } catch (error) {
      console.error('Get user profile error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Check account lockout status
  ipcMain.handle('check-account-status', async (event, username: string) => {
    try {
      console.log('Checking account status for username:', username);
      const client = await pool.connect();

      const result = await client.query(
        'SELECT username, failed_login_attempts, locked_until, last_failed_login FROM m_user WHERE username = $1',
        [username]
      );

      client.release();

      if (result.rows.length > 0) {
        const user = result.rows[0];
        const now = new Date();
        const isLocked = user.locked_until && new Date(user.locked_until) > now;

        return {
          success: true,
          accountStatus: {
            username: user.username,
            failedAttempts: user.failed_login_attempts,
            isLocked: isLocked,
            lockedUntil: user.locked_until,
            lastFailedLogin: user.last_failed_login,
            remainingLockoutTime: isLocked ? Math.ceil((new Date(user.locked_until).getTime() - now.getTime()) / (60 * 1000)) : 0
          }
        };
      } else {
        return {
          success: false,
          message: 'User not found'
        };
      }
    } catch (error) {
      console.error('Check account status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Unlock user account (admin function)
  ipcMain.handle('unlock-account', async (event, username: string) => {
    try {
      console.log('Unlocking account for username:', username);
      const client = await pool.connect();

      const result = await client.query(
        'UPDATE m_user SET failed_login_attempts = 0, locked_until = NULL, last_failed_login = NULL WHERE username = $1 RETURNING username',
        [username]
      );

      client.release();

      if (result.rows.length > 0) {
        console.log(`Account unlocked for user: ${username}`);
        return {
          success: true,
          message: `Account unlocked successfully for user: ${username}`
        };
      } else {
        return {
          success: false,
          message: 'User not found'
        };
      }
    } catch (error) {
      console.error('Unlock account error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  ipcMain.handle('fetch-data', async () => {
    try {
      console.log('fetch-data handler called');
      const response = await axios.get<Todo[]>('https://jsonplaceholder.typicode.com/todos');
      console.log('API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  });

  console.log('IPC handlers setup complete');
}

// Cleanup function to close database connections
export async function cleanupDatabase() {
  try {
    await pool.end();
    console.log('Database pool closed successfully');
  } catch (error) {
    console.error('Error closing database pool:', error);
  }
}