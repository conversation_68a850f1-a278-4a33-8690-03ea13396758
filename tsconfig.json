{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "node",
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noEmit": true,
    "declaration": false,
    "isolatedModules": true,
    "resolveJsonModule": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "*": [
        "./src/*"
      ],
      "~/*": [
        "./*"
      ]
    }
  },
  "include": [
    "src",
    "electron.vite.config.*",
    "index.d.ts",
    "tailwind.config.ts",
    "postcss.config.mjs",
  ],
  "exclude": [
    "node_modules"
  ],
}
